import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON><PERSON>eader,
  Di<PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Maximize2 } from "lucide-react";
import { Chart } from "./_Chart";
import { CandlestickData, HistogramData } from "lightweight-charts";

interface ChartModalProps {
  data: CandlestickData[];
  macdData: HistogramData[];
  title?: string;
  trigger?: React.ReactNode;
}

export const ChartModal = ({ 
  data, 
  macdData, 
  title = "Chart Details",
  trigger 
}: ChartModalProps) => {
  const [isOpen, setIsOpen] = useState(false);

  const defaultTrigger = (
    <Button variant="outline" size="sm">
      <Maximize2 className="h-4 w-4 mr-2" />
      Expand Chart
    </Button>
  );

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || defaultTrigger}
      </DialogTrigger>
      <DialogContent className="max-w-6xl w-full h-[80vh]">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
        </DialogHeader>
        <div className="flex-1 min-h-0">
          <Chart 
            data={data} 
            macdData={macdData} 
            height={600}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};
