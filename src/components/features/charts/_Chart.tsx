import { useEffect, useRef } from "react";
import {
  createChart,
  ColorType,
  IChartApi,
  CandlestickData,
  HistogramData,
  CandlestickSeries,
  HistogramSeries,
} from "lightweight-charts";

interface ChartProps {
  data: CandlestickData[];
  macdData: HistogramData[];
  width?: number;
  height?: number;
}

export const Chart = ({ data, macdData, width, height = 400 }: ChartProps) => {
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const chartRef = useRef<IChartApi | null>(null);

  useEffect(() => {
    if (!chartContainerRef.current) return;

    // Create the chart with dark theme
    const chart = createChart(chartContainerRef.current, {
      layout: {
        background: { type: ColorType.Solid, color: "hsl(var(--background))" },
        textColor: "hsl(var(--foreground))",
      },
      grid: {
        vertLines: { color: "hsl(var(--border))" },
        horzLines: { color: "hsl(var(--border))" },
      },
      crosshair: {
        mode: 0,
      },
      rightPriceScale: {
        borderColor: "hsl(var(--border))",
      },
      timeScale: {
        borderColor: "hsl(var(--border))",
        timeVisible: true,
        secondsVisible: false,
      },
      width: width || chartContainerRef.current.clientWidth,
      height,
    });

    chartRef.current = chart;

    // Add candlestick series for main price data using v5 API
    const candlestickSeries = chart.addSeries(CandlestickSeries, {
      upColor: "#22c55e",
      downColor: "#ef4444",
      borderVisible: false,
      wickUpColor: "#22c55e",
      wickDownColor: "#ef4444",
    });

    // Set the candlestick data
    candlestickSeries.setData(data);

    // Add MACD histogram series using v5 API
    const macdSeries = chart.addSeries(HistogramSeries, {
      color: "#8b5cf6",
      priceFormat: {
        type: "volume",
      },
    });

    // Set the MACD data
    macdSeries.setData(macdData);

    // Handle resize
    const handleResize = () => {
      if (chartContainerRef.current && chartRef.current) {
        chartRef.current.applyOptions({
          width: chartContainerRef.current.clientWidth,
        });
      }
    };

    window.addEventListener("resize", handleResize);

    // Cleanup function
    return () => {
      window.removeEventListener("resize", handleResize);
      if (chartRef.current) {
        chartRef.current.remove();
        chartRef.current = null;
      }
    };
  }, [data, macdData, width, height]);

  return (
    <div className="w-full h-full">
      <div ref={chartContainerRef} className="w-full" style={{ height }} />
    </div>
  );
};
