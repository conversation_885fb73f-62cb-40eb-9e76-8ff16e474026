import { useEffect, useRef, memo, useMemo } from "react";
import {
  create<PERSON><PERSON>,
  ColorType,
  IChartApi,
  CandlestickData,
  HistogramData,
  CandlestickSeries,
  HistogramSeries,
} from "lightweight-charts";
import { useChartState, ChartOptions } from "./stores";

interface ChartProps {
  data?: CandlestickData[];
  macdData?: HistogramData[];
  width?: number;
  height?: number;
  options?: Partial<ChartOptions>;
  onDataUpdate?: (data: CandlestickData[], macdData: HistogramData[]) => void;
  className?: string;
}

export const Chart = memo(
  ({
    data: propData,
    macdData: propMacdData,
    width,
    height,
    options: propOptions,
    onDataUpdate,
    className = "",
  }: ChartProps) => {
    const chartContainerRef = useRef<HTMLDivElement>(null);
    const chartRef = useRef<IChartApi | null>(null);

    // Use internal state management if no external data provided
    const { state, options, updateOptions, generateMockData } = useChartState(
      propData,
      propMacdData
    );

    // Use prop options if provided, otherwise use state options
    const chartOptions = useMemo(
      () => (propOptions ? { ...options, ...propOptions } : options),
      [options, propOptions]
    );
    const chartHeight = height || chartOptions.height;

    // Use external data if provided, otherwise use internal state
    const chartData = propData || state.data;
    const chartMacdData = propMacdData || state.macdData;

    // Generate initial data if none provided
    useEffect(() => {
      if (!propData && !propMacdData && state.data.length === 0) {
        generateMockData();
      }
    }, [propData, propMacdData, state.data.length, generateMockData]);

    // Update options when prop options change
    useEffect(() => {
      if (propOptions) {
        updateOptions(propOptions);
      }
    }, [propOptions, updateOptions]);

    // Notify parent of data updates
    useEffect(() => {
      if (onDataUpdate && chartData.length > 0 && chartMacdData.length > 0) {
        onDataUpdate(chartData, chartMacdData);
      }
    }, [chartData, chartMacdData, onDataUpdate]);

    useEffect(() => {
      if (!chartContainerRef.current || chartData.length === 0) return;

      // Create the chart with theme-based styling
      const chart = createChart(chartContainerRef.current, {
        layout: {
          background: {
            type: ColorType.Solid,
            color: chartOptions.theme === "dark" ? "hsl(var(--background))" : "#ffffff",
          },
          textColor: chartOptions.theme === "dark" ? "hsl(var(--foreground))" : "#000000",
        },
        grid: {
          vertLines: {
            color: chartOptions.theme === "dark" ? "hsl(var(--border))" : "#e1e5e9",
          },
          horzLines: {
            color: chartOptions.theme === "dark" ? "hsl(var(--border))" : "#e1e5e9",
          },
        },
        crosshair: {
          mode: 0,
        },
        rightPriceScale: {
          borderColor: chartOptions.theme === "dark" ? "hsl(var(--border))" : "#e1e5e9",
        },
        timeScale: {
          borderColor: chartOptions.theme === "dark" ? "hsl(var(--border))" : "#e1e5e9",
          timeVisible: true,
          secondsVisible: false,
        },
        width: width || chartContainerRef.current.clientWidth,
        height: chartHeight,
      });

      chartRef.current = chart;

      // Add candlestick series for main price data
      const candlestickSeries = chart.addSeries(CandlestickSeries, {
        upColor: "#22c55e",
        downColor: "#ef4444",
        borderVisible: false,
        wickUpColor: "#22c55e",
        wickDownColor: "#ef4444",
      });

      // Set the candlestick data
      candlestickSeries.setData(chartData);

      // Add MACD histogram series if enabled and data available
      if (chartOptions.showMACD && chartMacdData.length > 0) {
        const macdSeries = chart.addSeries(HistogramSeries, {
          color: "#8b5cf6",
          priceFormat: {
            type: "volume",
          },
        });

        // Set the MACD data
        macdSeries.setData(chartMacdData);
      }

      // Handle resize
      const handleResize = () => {
        if (chartContainerRef.current && chartRef.current) {
          chartRef.current.applyOptions({
            width: chartContainerRef.current.clientWidth,
          });
        }
      };

      window.addEventListener("resize", handleResize);

      // Cleanup function
      return () => {
        window.removeEventListener("resize", handleResize);
        if (chartRef.current) {
          chartRef.current.remove();
          chartRef.current = null;
        }
      };
    }, [chartData, chartMacdData, width, chartHeight, chartOptions]);

    // Show loading state
    if (state.isLoading) {
      return (
        <div
          className={`w-full flex items-center justify-center ${className}`}
          style={{ height: chartHeight }}
        >
          <div className="text-muted-foreground">Loading chart data...</div>
        </div>
      );
    }

    // Show error state
    if (state.error) {
      return (
        <div
          className={`w-full flex items-center justify-center ${className}`}
          style={{ height: chartHeight }}
        >
          <div className="text-red-500">Error: {state.error}</div>
        </div>
      );
    }

    return (
      <div className={`w-full h-full ${className}`}>
        <div ref={chartContainerRef} className="w-full" style={{ height: chartHeight }} />
      </div>
    );
  }
);

Chart.displayName = "Chart";
