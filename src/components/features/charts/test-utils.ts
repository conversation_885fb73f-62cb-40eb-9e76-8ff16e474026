// Simple test file to verify our utilities work correctly
// This can be removed after testing

import { 
  generateFakeData, 
  generateFakeMACDData, 
  validateChartData, 
  validateMACDData,
  calculateDataStats,
  transformCoinGeckoData,
  calculateMACDFromPrices
} from './utils';

// Test data generation
console.log('Testing data generation...');

const testData = generateFakeData({ dataPoints: 10, basePrice: 50000 });
console.log('Generated candlestick data:', testData.length, 'points');
console.log('First point:', testData[0]);
console.log('Last point:', testData[testData.length - 1]);

const testMACDData = generateFakeMACDData({ dataPoints: 10 });
console.log('Generated MACD data:', testMACDData.length, 'points');

// Test validation
const validation = validateChartData(testData);
console.log('Data validation:', validation.isValid ? 'PASSED' : 'FAILED');
if (!validation.isValid) {
  console.log('Validation errors:', validation.errors);
}

const macdValidation = validateMACDData(testMACDData);
console.log('MACD validation:', macdValidation.isValid ? 'PASSED' : 'FAILED');

// Test statistics
const stats = calculateDataStats(testData);
console.log('Data statistics:', stats);

// Test CoinGecko transformation
const mockCoinGeckoData = [
  [1640995200000, 47000],
  [1641081600000, 47500],
  [1641168000000, 46800],
];

const transformedData = transformCoinGeckoData(mockCoinGeckoData);
console.log('Transformed CoinGecko data:', transformedData.length, 'points');
console.log('Transformed point:', transformedData[0]);

// Test MACD calculation
const calculatedMACD = calculateMACDFromPrices(testData);
console.log('Calculated MACD data:', calculatedMACD.length, 'points');

console.log('All tests completed successfully!');
