import { useState, useCallback } from "react";
import { CandlestickData, HistogramData } from "lightweight-charts";

// Chart state interfaces
export interface ChartState {
  data: CandlestickData[];
  macdData: HistogramData[];
  isLoading: boolean;
  error: string | null;
  lastUpdated: Date | null;
}

export interface ChartOptions {
  height: number;
  showMACD: boolean;
  theme: 'light' | 'dark';
  autoRefresh: boolean;
  refreshInterval: number; // in milliseconds
}

// Default chart options
const defaultChartOptions: ChartOptions = {
  height: 400,
  showMACD: true,
  theme: 'dark',
  autoRefresh: false,
  refreshInterval: 300000, // 5 minutes
};

// Chart state hook
export const useChartState = (initialData?: CandlestickData[], initialMACDData?: HistogramData[]) => {
  const [state, setState] = useState<ChartState>({
    data: initialData || [],
    macdData: initialMACDData || [],
    isLoading: false,
    error: null,
    lastUpdated: null,
  });

  const [options, setOptions] = useState<ChartOptions>(defaultChartOptions);

  const updateData = useCallback((data: CandlestickData[], macdData: HistogramData[]) => {
    setState(prev => ({
      ...prev,
      data,
      macdData,
      lastUpdated: new Date(),
      error: null,
    }));
  }, []);

  const setLoading = useCallback((isLoading: boolean) => {
    setState(prev => ({ ...prev, isLoading }));
  }, []);

  const setError = useCallback((error: string | null) => {
    setState(prev => ({ ...prev, error, isLoading: false }));
  }, []);

  const updateOptions = useCallback((newOptions: Partial<ChartOptions>) => {
    setOptions(prev => ({ ...prev, ...newOptions }));
  }, []);

  return {
    state,
    options,
    updateData,
    setLoading,
    setError,
    updateOptions,
  };
};

// Chart configuration hook for persistent settings
export const useChartConfig = () => {
  const [config, setConfig] = useState(() => {
    // Load from localStorage if available
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('chartConfig');
      if (saved) {
        try {
          return JSON.parse(saved);
        } catch {
          return defaultChartOptions;
        }
      }
    }
    return defaultChartOptions;
  });

  const updateConfig = useCallback((newConfig: Partial<ChartOptions>) => {
    const updated = { ...config, ...newConfig };
    setConfig(updated);
    
    // Save to localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem('chartConfig', JSON.stringify(updated));
    }
  }, [config]);

  return { config, updateConfig };
};
