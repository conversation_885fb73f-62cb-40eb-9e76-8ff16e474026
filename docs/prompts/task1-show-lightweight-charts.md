Refactor the existing `src/pages/Index.tsx` component and create a new feature-based architecture for chart functionality. Follow these specific requirements:

1. **Create a new feature structure** under `src/components/features/charts/` with the following organization:

   ```
   src/components/features
   ├── charts/
   │   ├── _Chart.tsx(Parent component use underscore in head)
   │   ├── ChartModal.tsx ( Children component is below of parent)
   │   ├── utils.ts
   │   └── stores.ts
   ```

2. **Main Chart Component Requirements**:

   - Create `ChartContainer.tsx` as the primary chart component using lightweight-charts library
   - Support both stock and cryptocurrency data visualization (BTC, ETH, etc.)
   - Include proper TypeScript types using `CandlestickData`, `HistogramData`, and `UTCTimestamp` from lightweight-charts
   - Implement responsive design that adapts to container size

3. **Data Management**:

   - Move chart data generation logic to `utils.ts`
   - Implement data fetching functions in `data/apis.ts` for future API integration
     - Use CoinGecko APi to fetch 200day BTC real data and show
       create new api in here: /Users/<USER>/Desktop/ws/signal-sight-portfolio-tracker/supabase/functions
       after create, let supabase deploy
       CoinGecko api: https://api.coingecko.com/api/v3/coins/bitcoin/market_chart?vs_currency=usd&days=200&interval=daily

4. **Update Index Page**:

   - Refactor `src/pages/Index.tsx` to use the new chart feature components
   - Remove the existing chart data generation functions from Index.tsx
   - Import and use the new `ChartContainer` component instead of the current `AssetChart`

5. **Maintain Existing Functionality**:

   - Preserve all current chart features (candlestick display, MACD histogram, etc.)
   - Ensure the chart still renders properly with the mock data
   - Keep the same visual styling and behavior

6. **TypeScript Compliance**:
   - Ensure all new components pass `npx tsc --noEmit` without errors
   - Use proper typing throughout, avoiding any `any` types
   - Follow the established TypeScript patterns from our recent type safety improvements
